'use client';

import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import '../styles/components/languageToggle.scss';

export default function LanguageToggle() {
  const { i18n } = useTranslation();
  const [isEnglish, setIsEnglish] = useState(false);

  useEffect(() => {
    // Set initial state based on current language
    setIsEnglish(i18n.language === 'en');
  }, [i18n.language]);

  const toggleLanguage = () => {
    const newLang = isEnglish ? 'es' : 'en';
    i18n.changeLanguage(newLang);
    setIsEnglish(!isEnglish);
    
    // Store in localStorage
    localStorage.setItem('i18nextLng', newLang);
  };

  return (
    <div className="language-toggle">
      <span className={`language-label ${!isEnglish ? 'active' : ''}`}>ES</span>
      <label className="toggle-switch">
        <input
          type="checkbox"
          checked={isEnglish}
          onChange={toggleLanguage}
          aria-label="Toggle language"
        />
        <span className="slider"></span>
      </label>
      <span className={`language-label ${isEnglish ? 'active' : ''}`}>EN</span>
    </div>
  );
}
