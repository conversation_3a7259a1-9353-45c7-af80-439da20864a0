'use client';

import { useTranslation } from 'react-i18next';
import { useEffect } from 'react';

export default function DynamicHtml() {
  const { i18n } = useTranslation();

  useEffect(() => {
    // Update HTML lang attribute
    if (typeof document !== 'undefined') {
      document.documentElement.lang = i18n.language;
    }

    // Update document title based on language
    if (typeof document !== 'undefined') {
      const title = i18n.language === 'es' 
        ? 'Rocketly - Desarrollo Web' 
        : 'Rocketly - Web Development';
      document.title = title;
    }
  }, [i18n.language]);

  return null;
}
