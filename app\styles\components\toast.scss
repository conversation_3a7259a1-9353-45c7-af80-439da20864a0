@import '../variables';

.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  min-width: 300px;
  max-width: 500px;
  padding: 0;
  border-radius: $button-radius;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: $button-border;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &.toast-visible {
    transform: translateX(0);
    opacity: 1;
  }

  .toast-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    background: rgba($bg-secondary, 0.95);
    border-radius: $button-radius;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: $brand-gradient;
    }
  }

  .toast-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
  }

  .toast-message {
    color: $text-primary;
    font-size: 0.95rem;
    font-weight: 500;
    flex: 1;
    line-height: 1.4;
  }

  .toast-close {
    background: none;
    border: none;
    color: $text-secondary;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    flex-shrink: 0;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: $text-primary;
    }
  }

  // Success variant
  &.toast-success {
    .toast-content::before {
      background: $accent-green;
    }
  }

  // Error variant
  &.toast-error {
    .toast-content::before {
      background: #ff6b6b;
    }
  }

  // Info variant
  &.toast-info {
    .toast-content::before {
      background: $accent-blue;
    }
  }
}

// Mobile responsiveness
@media (max-width: $breakpoint-sm) {
  .toast {
    right: 10px;
    left: 10px;
    min-width: auto;
    max-width: none;
    transform: translateY(-100%);

    &.toast-visible {
      transform: translateY(0);
    }
  }
}
