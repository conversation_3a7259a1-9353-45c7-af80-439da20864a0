@import '../variables';

.language-toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-left: 1rem;

  .language-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.6);
    transition: all 0.3s ease;
    user-select: none;

    &.active {
      color: $brand-primary;
      text-shadow: 0 0 8px rgba(0, 198, 255, 0.4);
    }
  }

  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    cursor: pointer;

    input {
      opacity: 0;
      width: 0;
      height: 0;

      &:checked + .slider {
        background: linear-gradient(135deg, $brand-primary, $brand-secondary);
        box-shadow: 0 0 20px rgba(0, 198, 255, 0.3);

        &:before {
          transform: translateX(26px);
          background: linear-gradient(135deg, #ffffff, #f0f0f0);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
      }

      &:focus + .slider {
        box-shadow: 0 0 0 2px rgba(0, 198, 255, 0.2);
      }
    }

    .slider {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, #4a5568, #2d3748);
      border-radius: 24px;
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid rgba(255, 255, 255, 0.1);

      &:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 2px;
        background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
        border-radius: 50%;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    gap: 0.5rem;
    margin-left: 0.5rem;

    .language-label {
      font-size: 0.8rem;
    }

    .toggle-switch {
      width: 44px;
      height: 22px;

      input:checked + .slider:before {
        transform: translateX(22px);
      }

      .slider:before {
        height: 16px;
        width: 16px;
        left: 3px;
        bottom: 2px;
      }
    }
  }

  // Animation for language change
  &.changing {
    .language-label {
      animation: pulse 0.6s ease-in-out;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .language-toggle {
    .language-label {
      color: rgba(255, 255, 255, 0.7);

      &.active {
        color: $brand-primary;
      }
    }

    .toggle-switch .slider {
      background: linear-gradient(135deg, #2d3748, #1a202c);
      border-color: rgba(255, 255, 255, 0.2);

      &:before {
        background: linear-gradient(135deg, #f7fafc, #edf2f7);
      }
    }
  }
}
