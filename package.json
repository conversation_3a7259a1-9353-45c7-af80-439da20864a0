{"name": "rocketly", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@next/swc-wasm-nodejs": "13.5.1", "@react-email/html": "^0.0.11", "@react-email/render": "^1.2.1", "@types/node": "20.6.2", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "eslint": "8.49.0", "eslint-config-next": "13.5.1", "framer-motion": "^12.12.1", "i18next": "^25.4.2", "i18next-browser-languagedetector": "^8.2.0", "lottie-react": "^2.4.1", "lucide-react": "^0.446.0", "motion-plus": "^0.1.9", "next": "13.5.1", "postcss": "8.4.30", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "^15.7.3", "react-intersection-observer": "^9.16.0", "resend": "^6.0.2", "sass": "^1.71.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "typescript": "5.2.2", "vanta": "^0.5.24"}, "devDependencies": {"@types/three": "^0.176.0"}}