@import '../variables';

.services {
  padding: 6rem 0;
  color: #ffffff;
  position: relative;
  z-index: 1;

  &__subtitle {
    color: #aaa;
    font-size: 1.2rem;
    text-align: center;
    margin-bottom: 3rem;
  }

  &__grid {
    display: flex;
    flex-wrap: wrap;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 2rem;
    justify-content: center;
  }

  &__card {
    background: rgba(255, 255, 255, 0.05);
    max-width: 30%;
    min-width: 22rem;
    min-height: 16rem;
    border: $button-border;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: $button-transition;
    box-shadow: $button-shadow;

    &:hover {
      transform: translateY(-4px);
      box-shadow: $button-shadow-hover, 0 0 20px $brand-glow-light;
      border: $button-border-hover;
      background: rgba(255, 255, 255, 0.07);
    }

    &-icon {
      margin-bottom: 1.5rem;
      color: $brand-primary-dark;
    }

    h3 {
      font-size: 1.25rem;
      margin-bottom: 1rem;
    }

    p {
      font-size: 0.95rem;
      color: #ccc;
    }
  }
}
