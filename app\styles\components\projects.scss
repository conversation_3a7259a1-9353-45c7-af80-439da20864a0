@import '../variables';

.projects {
  position: relative;
  padding: 6rem 0;

  &__container {
    // Container para las animaciones de Framer Motion
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: $spacing-xl;
  }

  .__cards__container {
    display: flex;
    flex-direction: column;
    gap: $spacing-xl;
  }

  &__card {
    overflow: hidden;
    transition: $button-transition;
    // border: $button-border;
    // box-shadow: $button-shadow;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-3px);
      // border: $button-border-hover;
      // box-shadow: $button-shadow-hover, 0 0 15px $brand-glow-light;
      // background-color: lighten($bg-secondary, 2%);
    }

    &-image {
      width: 100%;
      height: 80vh;
      object-fit: cover;
      object-position: top;
      border: solid #596069 1px;
      border-radius: 8px;
    }

    &-content {
      padding: 24px 0px;

      h3 {
        margin-bottom: $spacing-sm;
      }

      p {
        color: $text-secondary;
        margin-bottom: $spacing-md;
      }
    }

    &-tags {
      display: flex;
      gap: $spacing-xs;
      flex-wrap: wrap;

      span {
        background-color: rgba($brand-primary, 0.2);
        color: $brand-primary-dark;
        padding: 4px 8px;
        border-radius: 8px;
        font-size: 0.875rem;
      }
    }

    &-buttons {
      margin-top: 15px;

      .button {
        padding: 10px 15px;
        border-radius: $button-radius;
        text-decoration: none;
        transition: $button-transition;
        font-weight: 500;
        font-family: 'Space Grotesk', sans-serif;
        position: relative;
        overflow: hidden;
        box-shadow: $button-shadow;
        cursor: pointer;
        font-size: 0.875rem;

        &--primary {
          background: #006942;
          color: white;
          border: none;
          font-weight: 600;
          box-shadow: inset 0 0px 4px #01af6f;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            transition: $button-transition;
            z-index: -1;
            opacity: 0;
          }

          &:hover {
            transform: translateY(-1px);
            box-shadow: $button-shadow-hover, 0 0 15px $brand-glow-light, inset 0 0px 4px #01af6f;

            &::before {
              opacity: 1;
            }
          }
        }

        &--secondary {
          background: rgba($bg-secondary, 0.6);
          color: $text-primary;
          border: $button-border;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba($bg-secondary, 0.8);
            transition: $button-transition;
            z-index: -1;
            opacity: 0;
          }

          &:hover {
            transform: translateY(-1px);
            box-shadow: $button-shadow-hover;
            border: $button-border-hover;
            color: white;

            &::before {
              opacity: 1;
            }
          }
        }
      }
    }
  }
}