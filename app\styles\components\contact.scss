@import '../variables';

.contact-section {
  position: relative;
  padding: 80px 20px;
  background: linear-gradient(to bottom, #000000 0%, #001f3f 100%);
  color: #fff;
  text-align: center;
  overflow: hidden;
  width: 100%;

  .contact-overlay {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at center, $brand-glow, transparent 70%),
      url('data:image/svg+xml;utf8,<svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="none" stroke="%231a1a1a" stroke-width="1" /></svg>');
    background-size: cover;
    pointer-events: none;
    z-index: 0;
  }

  .contact-content {
    position: relative;
    z-index: 1;
    max-width: 600px;
    margin: 0 auto;

    h1 {
      font-size: 3rem;
      margin-bottom: 20px;

      span {
        background: $brand-gradient;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    p {
      font-size: 1.1rem;
      color: #ccc;
      margin-bottom: 40px;
    }

    .contact-form {
      display: flex;
      flex-direction: column;
      gap: 20px;

      input,
      textarea {
        padding: 15px;
        border: $button-border;
        border-radius: $button-radius;
        font-size: 1rem;
        background: rgba(255, 255, 255, 0.05);
        color: white;
        outline: none;
        transition: $button-transition;
        font-family: inherit;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          background: rgba(255, 255, 255, 0.08);
          border: $button-border-hover;
          box-shadow: 0 0 0 2px $brand-glow-light;
        }
      }

      button {
        background: #006942;
        border: none;
        padding: 15px;
        font-size: 1rem;
        color: white;
        font-family: 'Space Grotesk', sans-serif;
        border-radius: $button-radius;
        cursor: pointer;
        transition: $button-transition;
        font-weight: 600;
        box-shadow: inset 0 0px 4px #01af6f;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(255, 255, 255, 0.1);
          transition: $button-transition;
          z-index: -1;
          opacity: 0;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: $button-shadow-hover, 0 0 15px $brand-glow-light, inset 0 0px 4px #01af6f;

          &::before {
            opacity: 1;
          }
        }
      }
    }
  }
}