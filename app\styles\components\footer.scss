@import '../variables';

.footer-section {
  position: relative;
  padding: 20px 20px 40px;
  background: #001f3f;
  color: #fff;
  text-align: center;
  overflow: hidden;
  width: 100%;

  .footer-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    pointer-events: none;
    z-index: 0;
  }

  .footer-content {
    position: relative;
    z-index: 1;
    max-width: 600px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 30px;
    align-items: center;

    .footer-social {
      display: flex;
      gap: 24px;
      justify-content: center;
      align-items: center;

      .social-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        border: $button-border;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.05);
        color: #fff;
        text-decoration: none;
        transition: $button-transition;
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border: $button-border-hover;
          transform: translateY(-2px);
          box-shadow: 0 4px 20px $brand-primary-dark;
          
          svg {
            color: $brand-primary-dark;
          }
        }

        svg {
          transition: color 0.3s ease;
        }
      }

      @media (max-width: $breakpoint-sm) {
        gap: 16px;

        .social-link {
          width: 44px;
          height: 44px;

          svg {
            width: 20px;
            height: 20px;
          }
        }
      }
    }

    .footer-text {
      p {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.6);
        margin: 0;
        font-weight: 400;
      }
    }
  }

  .stars {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
    z-index: 0;

    .star {
      position: absolute;
      width: 2px;
      height: 2px;
      background: white;
      border-radius: 50%;
      animation: floatStar linear infinite;
      opacity: 0.6;

      @for $i from 1 through 30 {
        &:nth-child(#{$i}) {
          top: random(100) * 1%;
          left: random(100) * 1%;
          animation-duration: #{6 + random(8)}s;
          animation-delay: -#{random(8)}s;
          transform: scale(#{0.4 + random(4) / 10});
        }
      }
    }
  }

  @media (max-width: $breakpoint-md) {
    padding: 20px 20px 30px;
    
    .footer-content {
      gap: 24px;
    }
  }
}

@keyframes floatStar {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.4;
  }

  50% {
    transform: translateY(50px) scale(1.1);
    opacity: 0.8;
  }

  100% {
    transform: translateY(0) scale(1);
    opacity: 0.4;
  }
}
